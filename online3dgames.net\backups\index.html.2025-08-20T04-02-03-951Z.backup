<!DOCTYPE html>
<html lang="en">

<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WZL8VKTQ0M"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WZL8VKTQ0M');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Blackjack 21">
    <meta name="application-name" content="Blackjack 21">
    <meta name="theme-color" content="#1a4b3a">
    <meta name="msapplication-TileColor" content="#1a4b3a">
    <meta name="msapplication-navbutton-color" content="#1a4b3a">
    <meta name="screen-orientation" content="landscape">
    <meta name="orientation" content="landscape">

    <!-- Prevent reader mode -->
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="robots" content="noarchive">
    <meta property="og:type" content="game">
    <meta name="application-type" content="game">

    <title>Blackjack Online - 24/7 Free Play Card Games</title>
    <meta name="description" content="Experience the most professional Blackjack 21 game with authentic casino tables, smooth dealing animations, and genuine game rules.">
    <meta name="keywords" content="blackjack, 21, casino game, card game, online blackjack, casino experience">
    <link rel="stylesheet" href="/assets/css/common-buttons.css">
    <link rel="stylesheet" href="/assets/css/unified-settings-modal.css">
    <link rel="stylesheet" href="/blackjack-simulator/css/styles.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <script src="/assets/js/i18n.js"></script>
    <script src="/assets/js/unified-settings-modal.js"></script>
</head>

<body role="application">
    <!-- SEO H1 for search engines (hidden from users) -->
    <h1 style="position: absolute; left: -9999px; top: -9999px; visibility: hidden;" data-i18n="ui.seoH1">Blackjack 21 - Professional Casino Card Game</h1>

    <div class="rotate-prompt" id="rotate-prompt"> 
        <div class="rotate-content">
            <div class="rotate-icon">📱</div> 
            <div class="rotate-text" data-i18n="ui.rotatePrompt">Please rotate your device to landscape mode for the best gaming experience</div>
            <div class="rotate-arrow">↻</div>
        </div>
    </div>

    <div class="casino-container">
        <div class="top-status">
            <div class="right-controls">
                <button class="settings-button" id="settings-button" title="Game Settings" data-i18n="ui.settingsButton">
                    <span class="settings-icon">⚙️</span>
                </button>
            </div>
        </div>

        <div class="deck-area" style="display: none;">
            <div class="deck-container" id="main-deck">
                <div class="deck-pile">
                    <div class="card card-back deck-card"></div>
                    <div class="deck-info">
                        <div class="deck-label" data-i18n="ui.cardsLeft">Cards Left</div>
                        <div class="deck-count" id="deck-count">312</div>
                        <div class="deck-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="deck-progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="deck-container" id="discard-pile">
                <div class="deck-pile">
                    <div class="card card-back deck-card discard-card"></div>
                    <div class="deck-info">
                        <div class="deck-label" data-i18n="ui.discardPile">Discard Pile</div>
                        <div class="deck-count" id="discard-count">0</div>
                        <div class="deck-progress">
                            <div class="progress-bar">
                                <div class="progress-fill discard-fill" id="discard-progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="casino-table">
            <div class="achievement-panel" id="achievement-panel" style="display: none;">
                <div class="achievement-header">
                    <span class="achievement-title" data-i18n="ui.achievements">🏆 Achievements</span>
                </div>
                <div class="achievement-stats">
                    <div class="achievement-item">
                        <span class="achievement-label" data-i18n="ui.highestWin">Highest Win:</span>
                        <span class="achievement-value" id="highest-win">0</span>
                    </div>
                    <div class="achievement-item">
                        <span class="achievement-label" data-i18n="ui.winRate">Win Rate:</span>
                        <span class="achievement-value" id="win-rate">0%</span>
                    </div>
                    <div class="achievement-item">
                        <span class="achievement-label" data-i18n="ui.netProfit">Net Profit:</span>
                        <span class="achievement-value" id="net-profit">0</span>
                    </div>
                    <div class="achievement-item">
                        <span class="achievement-label" data-i18n="ui.bestStreak">Best Streak:</span>
                        <span class="achievement-value" id="best-streak">0</span>
                    </div>
                    <div class="achievement-item">
                        <span class="achievement-label" data-i18n="ui.totalGames">Total Games:</span>
                        <span class="achievement-value" id="total-games">0</span>
                    </div>
                </div>
            </div>

            <div class="dealer-section">
                <div class="dealer-info">
                    <div class="dealer-avatar">
                        <img src="/blackjack-simulator/images/dealer.png" alt="dealer">
                        <span class="dealer-name" data-i18n="ui.dealer">Dealer</span>
                    </div>
                    <div class="dealer-score">
                        <span id="dealer-score">0</span>
                    </div>
                    <div class="betting-countdown" id="betting-countdown" style="display: none;">
                        <div class="countdown-text" data-i18n="ui.bettingCountdown">Please place a bet......</div>
                        <div class="countdown-timer" id="countdown-timer">15</div>
                    </div>
                </div>
                <div class="dealer-cards-container">
                    <div class="dealer-cards-area" id="dealer-cards">
                    </div>
                </div>
            </div>

            
            <div class="table-center">
                <div class="game-settings-container" id="game-settings-container">
                    <div class="settings-panel">
                        <h2 class="settings-title" data-i18n="settings.title">Game Settings</h2>

                        <div class="ios-fullscreen-tip" id="ios-fullscreen-tip" style="display: none;">
                            <div class="tip-icon">📱</div>
                            <div class="tip-text">
                                <span data-i18n="ui.iosFullscreenTip">For the best experience, add this game to your home screen for fullscreen play!</span>
                                <br><small data-i18n="ui.iosFullscreenTipSmall">Tap Share → Add to Home Screen</small>
                            </div>
                        </div>

                        <div class="setting-group">
                            <label class="setting-label" data-i18n="ui.numberOfPlayers">Number of Players</label>
                            <div class="setting-options">
                                <button class="setting-btn" data-players="1">
                                    <span class="option-number">1</span>
                                    <span class="option-text" data-i18n="ui.player">Player</span>
                                </button>
                                <button class="setting-btn active" data-players="3">
                                    <span class="option-number">3</span>
                                    <span class="option-text" data-i18n="ui.players">Players</span>
                                </button>
                                <button class="setting-btn" data-players="4">
                                    <span class="option-number">4</span>
                                    <span class="option-text" data-i18n="ui.players">Players</span>
                                </button>
                                <button class="setting-btn six-player" data-players="6">
                                    <span class="option-number">6</span>
                                    <span class="option-text" data-i18n="ui.players">Players</span>
                                </button>
                            </div>
                        </div>

                        <div class="setting-group">
                            <label class="setting-label" data-i18n="ui.numberOfDecks">Number of Decks</label>
                            <div class="setting-options">
                                <button class="setting-btn" data-decks="1">
                                    <span class="option-number">1</span>
                                    <span class="option-text" data-i18n="ui.deck">Deck</span>
                                </button>
                                <button class="setting-btn" data-decks="2">
                                    <span class="option-number">2</span>
                                    <span class="option-text" data-i18n="ui.decks">Decks</span>
                                </button>
                                <button class="setting-btn active" data-decks="6">
                                    <span class="option-number">6</span>
                                    <span class="option-text" data-i18n="ui.decks">Decks</span>
                                </button>
                            </div>
                        </div>

                        <button class="start-game-btn" id="start-game-btn">
                            <div class="btn-text" data-i18n="ui.startGame">START GAME</div>
                        </button>
                    </div>
                </div>

                <div class="game-status" id="game-status" style="display: none;">Place your bets to start</div>
            </div>

            
            <div class="players-area">
                <div class="player-position" data-position="0">
                    <div class="player-cards-container">
                        <div class="player-cards-area" id="player-cards-0">
                        </div>
                        <div class="player-score hidden">
                            <span id="player-score-0">0</span>
                        </div>
                    </div>
                    <div class="bet-spot">
                        <div class="bet-circle" data-position="0">
                            <div class="bet-amount" id="bet-amount-0">0</div>
                        </div>
                        <div class="side-bet-spot perfectPairs" data-type="perfectPairs">
                            <div class="side-bet-label" data-i18n="ui.perfectPairs">Perfect Pairs</div>
                            <div class="side-bet-amount" id="perfect-pairs-bet-0">0</div>
                        </div>
                        <div class="side-bet-spot twentyOnePlusThree" data-type="twentyOnePlusThree">
                            <div class="side-bet-label" data-i18n="ui.twentyOnePlusThree">21+3</div>
                            <div class="side-bet-amount" id="twenty-one-plus-three-bet-0">0</div>
                        </div>
                    </div>
                    <div class="player-info">
                        <div class="player-avatar">
                            <img src="/blackjack-simulator/images/player1.png" alt="player1">
                            <span class="player-name">player1</span>
                        </div>
                        <div class="action-bubble" id="action-bubble-0"></div>
                    </div>
                    <div class="player-balance" style="display: none;"><span id="player-balance-0">500000</span></div>
                </div>

                <div class="player-position current-player" data-position="1">
                    <div class="player-cards-container">
                        <div class="player-cards-area" id="player-cards-1">
                        </div>
                        <div class="player-score hidden">
                            <span id="player-score-1">0</span>
                        </div>
                    </div>
                    <div class="bet-spot">
                        <div class="bet-circle active" data-position="1">
                            <div class="bet-amount" id="bet-amount-1">0</div>
                        </div>
                        <div class="side-bet-spot perfectPairs" data-type="perfectPairs">
                            <div class="side-bet-label" data-i18n="ui.perfectPairs">Perfect Pairs</div>
                            <div class="side-bet-amount" id="perfect-pairs-bet">0</div>
                        </div>
                        <div class="side-bet-spot twentyOnePlusThree" data-type="twentyOnePlusThree">
                            <div class="side-bet-label" data-i18n="ui.twentyOnePlusThree">21+3</div>
                            <div class="side-bet-amount" id="twenty-one-plus-three-bet">0</div>
                        </div>

                    </div>
                    <div class="player-info">
                        <div class="player-avatar">
                            <img src="/blackjack-simulator/images/user.png" alt="you">
                            <span class="player-name" data-i18n="ui.you">you</span>
                        </div>
                        <div class="action-bubble" id="action-bubble-1"></div>
                    </div>
                    <div class="player-balance"><span id="player-balance">500000</span></div>
                </div>

                <div class="player-position" data-position="2">
                    <div class="player-cards-container">
                        <div class="player-cards-area" id="player-cards-2">
                        </div>
                        <div class="player-score hidden">
                            <span id="player-score-2">0</span>
                        </div>
                    </div>
                    <div class="bet-spot">
                        <div class="bet-circle" data-position="2">
                            <div class="bet-amount" id="bet-amount-2">0</div>
                        </div>
                        <div class="side-bet-spot perfectPairs" data-type="perfectPairs">
                            <div class="side-bet-label" data-i18n="ui.perfectPairs">Perfect Pairs</div>
                            <div class="side-bet-amount" id="perfect-pairs-bet-2">0</div>
                        </div>
                        <div class="side-bet-spot twentyOnePlusThree" data-type="twentyOnePlusThree">
                            <div class="side-bet-label" data-i18n="ui.twentyOnePlusThree">21+3</div>
                            <div class="side-bet-amount" id="twenty-one-plus-three-bet-2">0</div>
                        </div>
                    </div>
                    <div class="player-info">
                        <div class="player-avatar">
                            <img src="/blackjack-simulator/images/player2.png" alt="player3">
                            <span class="player-name">player3</span>
                        </div>
                        <div class="action-bubble" id="action-bubble-2"></div>
                    </div>
                    <div class="player-balance" style="display: none;"><span id="player-balance-2">500000</span></div>
                </div>

                <div class="player-position" data-position="3" style="display: none;">
                    <div class="player-cards-container">
                        <div class="player-cards-area" id="player-cards-3">
                        </div>
                        <div class="player-score hidden">
                            <span id="player-score-3">0</span>
                        </div>
                    </div>
                    <div class="bet-spot">
                        <div class="bet-circle" data-position="3">
                            <div class="bet-amount" id="bet-amount-3">0</div>
                        </div>
                        <div class="side-bet-spot perfectPairs" data-type="perfectPairs">
                            <div class="side-bet-label" data-i18n="ui.perfectPairs">Perfect Pairs</div>
                            <div class="side-bet-amount" id="perfect-pairs-bet-3">0</div>
                        </div>
                        <div class="side-bet-spot twentyOnePlusThree" data-type="twentyOnePlusThree">
                            <div class="side-bet-label" data-i18n="ui.twentyOnePlusThree">21+3</div>
                            <div class="side-bet-amount" id="twenty-one-plus-three-bet-3">0</div>
                        </div>
                    </div>
                    <div class="player-info">
                        <div class="player-avatar">
                            <img src="/blackjack-simulator/images/player4.png" alt="player4">
                            <span class="player-name">player4</span>
                        </div>
                        <div class="action-bubble" id="action-bubble-3"></div>
                    </div>
                    <div class="player-balance" style="display: none;"><span id="player-balance-3">500000</span></div>
                </div>

                <div class="player-position" data-position="4" style="display: none;">
                    <div class="player-cards-container">
                        <div class="player-cards-area" id="player-cards-4">
                        </div>
                        <div class="player-score hidden">
                            <span id="player-score-4">0</span>
                        </div>
                    </div>
                    <div class="bet-spot">
                        <div class="bet-circle" data-position="4">
                            <div class="bet-amount" id="bet-amount-4">0</div>
                        </div>
                        <div class="side-bet-spot perfectPairs" data-type="perfectPairs">
                            <div class="side-bet-label" data-i18n="ui.perfectPairs">Perfect Pairs</div>
                            <div class="side-bet-amount" id="perfect-pairs-bet-4">0</div>
                        </div>
                        <div class="side-bet-spot twentyOnePlusThree" data-type="twentyOnePlusThree">
                            <div class="side-bet-label" data-i18n="ui.twentyOnePlusThree">21+3</div>
                            <div class="side-bet-amount" id="twenty-one-plus-three-bet-4">0</div>
                        </div>
                    </div>
                    <div class="player-info">
                        <div class="player-avatar">
                            <img src="/blackjack-simulator/images/player5.png" alt="player5">
                            <span class="player-name">player5</span>
                        </div>
                        <div class="action-bubble" id="action-bubble-4"></div>
                    </div>
                    <div class="player-balance" style="display: none;"><span id="player-balance-4">500000</span></div>
                </div>

                <div class="player-position" data-position="5" style="display: none;">
                    <div class="player-cards-container">
                        <div class="player-cards-area" id="player-cards-5">
                        </div>
                        <div class="player-score hidden">
                            <span id="player-score-5">0</span>
                        </div>
                    </div>
                    <div class="bet-spot">
                        <div class="bet-circle" data-position="5">
                            <div class="bet-amount" id="bet-amount-5">0</div>
                        </div>
                        <div class="side-bet-spot perfectPairs" data-type="perfectPairs">
                            <div class="side-bet-label" data-i18n="ui.perfectPairs">Perfect Pairs</div>
                            <div class="side-bet-amount" id="perfect-pairs-bet-5">0</div>
                        </div>
                        <div class="side-bet-spot twentyOnePlusThree" data-type="twentyOnePlusThree">
                            <div class="side-bet-label" data-i18n="ui.twentyOnePlusThree">21+3</div>
                            <div class="side-bet-amount" id="twenty-one-plus-three-bet-5">0</div>
                        </div>
                    </div>
                    <div class="player-info">
                        <div class="player-avatar">
                            <img src="/blackjack-simulator/images/player1.png" alt="player6">
                            <span class="player-name">player6</span>
                        </div>
                        <div class="action-bubble" id="action-bubble-5"></div>
                    </div>
                    <div class="player-balance" style="display: none;"><span id="player-balance-5">500000</span></div>
                </div>
            </div>
        </div>

        <div class="bottom-controls">
            <div class="chip-tray" id="betting-controls">
                <button id="clear-bet" class="action-btn clear-btn" title="Clear" data-i18n="ui.clear">
                    Clear
                </button>
                <button class="chip-btn" data-value="10000" title="10,000 Chip">
                    <div class="chip chip-10k">10,000</div>
                </button>
                <button class="chip-btn" data-value="20000" title="20,000 Chip">
                    <div class="chip chip-20k">20,000</div>
                </button>
                <button class="chip-btn" data-value="50000" title="50,000 Chip">
                    <div class="chip chip-50k">50,000</div>
                </button>
                <button class="chip-btn" data-value="100000" title="100,000 Chip">
                    <div class="chip chip-100k">100,000</div>
                </button>
                <button class="chip-btn" data-value="250000" title="250,000 Chip">
                    <div class="chip chip-250k">250,000</div>
                </button>
                <button class="chip-btn" data-value="500000" title="500,000 Chip">
                    <div class="chip chip-500k">500,000</div>
                </button>
                <button class="chip-btn" data-value="1000000" title="1,000,000 Chip">
                    <div class="chip chip-1m">1,000,000</div>
                </button>
                <button id="all-in" class="action-btn all-in-btn" title="All In" data-i18n="ui.allIn">
                    All In
                </button>
                <button id="deal-cards" class="action-btn deal-btn" title="Deal" style="display: none;">
                    <!-- <span class="btn-icon">🎴</span> -->
                    <span class="btn-text" data-i18n="ui.deal">Deal</span>
                </button>
            </div>

            <div class="action-controls" id="action-controls" style="display: none;">
                <button id="double-down" class="action-btn double-btn">
                    <span class="btn-icon">×2</span>
                    <span class="btn-text" data-i18n="ui.double">Double Down</span>
                </button>
                <button id="split" class="action-btn split-btn" style="display: none;">
                    <span class="btn-icon">&lt;&gt;</span>
                    <span class="btn-text" data-i18n="ui.split">Split</span>
                </button>
                <button id="hit" class="action-btn hit-btn">
                    <span class="btn-icon">🎯</span>
                    <span class="btn-text" data-i18n="ui.hit">Hit</span>
                </button>
                <button id="stand" class="action-btn stand-btn">
                    <span class="btn-icon">✋</span>
                    <span class="btn-text" data-i18n="ui.stand">Stand</span>
                </button>
            </div>

            <div class="side-bet-controls" id="side-bet-controls" style="display: none;">
                <div class="side-bet-section">
                    <h4 data-i18n="ui.sideBets">Side Bets</h4>
                    <div class="side-bet-buttons">
                        <button class="side-bet-btn" data-type="perfectPairs" data-value="50">
                            <span data-i18n="ui.perfectPairs">Perfect Pairs</span> 50
                        </button>
                        <button class="side-bet-btn" data-type="perfectPairs" data-value="100">
                            <span data-i18n="ui.perfectPairs">Perfect Pairs</span> 100
                        </button>
                        <button class="side-bet-btn" data-type="twentyOnePlusThree" data-value="50">
                            <span data-i18n="ui.twentyOnePlusThree">21+3</span> 50
                        </button>
                        <button class="side-bet-btn" data-type="twentyOnePlusThree" data-value="100">
                            <span data-i18n="ui.twentyOnePlusThree">21+3</span> 100
                        </button>
                        <button id="clear-side-bets" class="action-btn clear-btn" data-i18n="ui.clearSideBets">
                            Clear Side Bets
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text" data-i18n="ui.dealing">Dealing...</div>
            </div>
        </div>

        <!-- Insurance Panel -->
        <div class="insurance-panel" id="insurance-panel" style="display: none;">
            <div class="insurance-content">
                <div class="insurance-title" data-i18n="ui.insuranceAvailable">Insurance Available</div>
                <div class="insurance-description" data-i18n="ui.insuranceDescription">Dealer shows an Ace. Buy insurance for half your bet?</div>
                <div class="insurance-amount"><span data-i18n="ui.insuranceCost">Insurance cost: $</span><span id="insurance-cost">0</span></div>
                <div class="insurance-buttons">
                    <button id="buy-insurance" class="insurance-btn buy-btn" data-i18n="ui.buyInsurance">Buy Insurance</button>
                    <button id="decline-insurance" class="insurance-btn decline-btn" data-i18n="ui.noInsurance">No Insurance</button>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div class="rules-modal" id="rules-modal" style="display: none;">
            <div class="rules-modal-content">
                <div class="rules-header">
                    <h2 data-i18n="rules.title">Blackjack Game Rules</h2>
                    <button class="rules-close" id="rules-close">&times;</button>
                </div>
                <div class="rules-body">
                    <div class="rules-section">
                        <h3 data-i18n="rules.objective.title">🎯 Game Objective</h3>
                        <p data-i18n="rules.objective.description">Get as close to 21 as possible without going over. Beat the dealer's hand to win!</p>
                    </div>

                    <div class="rules-section">
                        <h3 data-i18n="rules.cardValues.title">🃏 Card Values</h3>
                        <ul>
                            <li data-i18n="rules.cardValues.numberCards">Number cards (2-10): Face value</li>
                            <li data-i18n="rules.cardValues.faceCards">Face cards (J, Q, K): 10 points each</li>
                            <li data-i18n="rules.cardValues.ace">Ace: 1 or 11 points (whichever is better)</li>
                        </ul>
                    </div>

                    <div class="rules-section">
                        <h3 data-i18n="rules.gameActions.title">🎮 Game Actions</h3>
                        <ul>
                            <li><strong data-i18n="ui.hit">Hit</strong>: <span data-i18n="rules.gameActions.hit">Take another card</span></li>
                            <li><strong data-i18n="ui.stand">Stand</strong>: <span data-i18n="rules.gameActions.stand">Keep your current hand</span></li>
                            <li><strong data-i18n="ui.double">Double Down</strong>: <span data-i18n="rules.gameActions.doubleDown">Double your bet and take exactly one more card</span></li>
                            <li><strong data-i18n="ui.split">Split</strong>: <span data-i18n="rules.gameActions.split">Split identical cards into two separate hands (requires additional bet)</span></li>
                            <li><strong data-i18n="ui.surrender">Surrender</strong>: <span data-i18n="rules.gameActions.surrender">Give up your hand and get half your bet back</span></li>
                            <li><strong data-i18n="ui.insurance">Insurance</strong>: <span data-i18n="rules.gameActions.insurance">Protect against dealer blackjack (costs half your bet)</span></li>
                        </ul>
                    </div>

                    <div class="rules-section">
                        <h3 data-i18n="rules.payouts.title">💰 Payouts</h3>
                        <ul>
                            <li data-i18n="rules.payouts.blackjack">Blackjack (21 with 2 cards): 1.5:1 payout (bet × 2.5)</li>
                            <li data-i18n="rules.payouts.regularWin">Regular Win: 1:1 payout (bet × 2)</li>
                            <li data-i18n="rules.payouts.push">Push (Tie): Bet returned</li>
                            <li data-i18n="rules.payouts.bust">Bust (Over 21): Lose bet</li>
                            <li data-i18n="rules.payouts.insurance">Insurance: 2:1 payout if dealer has blackjack</li>
                        </ul>
                    </div>

                    <div class="rules-section">
                        <h3 data-i18n="rules.dealerRules.title">🎯 Dealer Rules</h3>
                        <ul>
                            <li data-i18n="rules.dealerRules.hitOn16">Dealer must hit on 16 or less</li>
                            <li data-i18n="rules.dealerRules.standOn17">Dealer must stand on 17 or more</li>
                            <li data-i18n="rules.dealerRules.hitSoft17">Dealer hits on soft 17 (Ace + 6)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="game-over-modal" class="modal" style="display: none;">
        <div class="modal-content game-over-content">
            <div class="game-over-header">
                <h2 data-i18n="gameOver.title">Game Over</h2>
                <div class="game-over-icon" data-i18n="gameOver.icon">💸</div>
            </div>
            <div class="game-over-message">
                <p class="main-message" data-i18n="gameOver.mainMessage">Your balance has run out!</p>
                <p class="encouragement-message" data-i18n="gameOver.encouragementMessage">Don't worry, every great player has faced setbacks. The key to success in Blackjack is learning from experience and managing your bankroll wisely.</p>
                <p class="tip-message" data-i18n="gameOver.tipMessage">💡 Pro Tip: Start with smaller bets and gradually increase as you build your confidence and skills!</p>
            </div>
            <div class="game-over-actions">
                <button id="restart-game-btn" class="restart-btn">
                    <span class="restart-icon">🎰</span>
                    <span data-i18n="gameOver.startFresh">Start Fresh</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Share Incentive Modal -->
    <div id="share-incentive-modal" class="modal" style="display: none;">
        <div class="modal-content share-incentive-content">
            <div class="share-incentive-header">
                <h2>🎁 Get 5000 Bonus Chips!</h2>
                <div class="share-incentive-icon">🎰</div>
            </div>
            <div class="share-incentive-message">
                <p class="main-message">Your balance has run out, but don't give up!</p>
                <p class="encouragement-message">Share our amazing Blackjack game with your friends and get <strong>5000 bonus chips</strong> to continue playing!</p>
                <p class="tip-message">💡 Help others discover this great game and earn rewards!</p>
            </div>
            <div class="share-section">
                <div class="share-text">
                    <p>Share on social media to get your bonus:</p>
                </div>
                <div class="share-buttons">
                    <button id="share-facebook-incentive" class="share-button facebook" title="Share on Facebook">
                        <img src="images/facebook.svg" alt="Facebook" />
                    </button>
                    <button id="share-twitter-incentive" class="share-button twitter" title="Share on Twitter">
                        <img src="images/twitter.svg" alt="Twitter" />
                    </button>
                    <button id="share-whatsapp-incentive" class="share-button whatsapp" title="Share on WhatsApp">
                        <img src="images/whatsapp.svg" alt="WhatsApp" />
                    </button>
                    <button id="share-telegram-incentive" class="share-button telegram" title="Share on Telegram">
                        <img src="images/telegram.svg" alt="Telegram" />
                    </button>
                </div>
            </div>
            <div class="share-incentive-actions">
                <button id="skip-share-btn" class="skip-share-btn">
                    Skip and Restart
                </button>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1;  border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);" data-i18n="seo.title">Professional Blackjack 21 - Authentic Casino Experience Online</h2>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);" data-i18n="seo.premiumCasino.title">🎰 Premium Casino Atmosphere</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;" data-i18n="seo.premiumCasino.description1">Experience the thrill of Las Vegas from your home with our professional-grade Blackjack game. Featuring authentic casino tables, realistic dealer animations, and immersive sound effects that recreate the excitement of a real casino floor.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;" data-i18n="seo.premiumCasino.description2">Our multi-player Blackjack supports up to 6 players simultaneously, creating a social gaming environment that mirrors the communal experience of playing at a physical casino table.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎲 Advanced Game Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Multi-Player Tables:</strong> Play with up to 6 players</li>
                    <li><strong style="color: #ffffff;">Side Bets:</strong> Perfect Pairs and 21+3 options</li>
                    <li><strong style="color: #ffffff;">Professional Dealers:</strong> Realistic dealing animations</li>
                    <li><strong style="color: #ffffff;">Achievement System:</strong> Track your progress and wins</li>
                    <li><strong style="color: #ffffff;">Landscape Mode:</strong> Optimized for tablet and desktop</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🃏 Side Bets & Advanced Gameplay</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Enhance your Blackjack experience with exciting side bets. Perfect Pairs pays up to 25:1 for matching cards, while 21+3 combines your first two cards with the dealer's up card to form poker hands, with payouts reaching 100:1 for suited three of a kind.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Our advanced betting system includes high-limit chips ranging from 10K to 1M, plus an all-in option for those dramatic moments. The achievement system tracks your biggest wins, win rates, and longest winning streaks.</p>
        </div>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">💎 High Roller Experience</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Starting balance: 500,000 chips</li>
                    <li>Maximum bet: 1,000,000 chips</li>
                    <li>VIP chip denominations available</li>
                    <li>Professional table limits</li>
                </ul>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 Tournament Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Competitive multi-player action</li>
                    <li>Real-time leaderboards</li>
                    <li>Achievement unlocking system</li>
                    <li>Statistical performance tracking</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌟 The Ultimate Blackjack Experience</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Our professional Blackjack game combines cutting-edge technology with traditional casino gameplay. Enjoy smooth card animations, intelligent AI opponents, and a sophisticated betting system that caters to both casual players and high rollers.</p>
            <p style="color: #f0f0f0;">Whether you're hosting a virtual casino night with friends or honing your skills for your next Vegas trip, our Blackjack game delivers an unparalleled online gaming experience. Join thousands of players who have made this their go-to Blackjack destination!</p>
        </div>
    </div>

                                    <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title" data-i18n="recommendations.moreBlackjackGames">More Blackjack Games</h3>
            <p class="recommendations-subtitle" data-i18n="recommendations.exploreVariants">Explore different Blackjack variants</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/blackjack-online" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/free-bet-blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-card-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title" data-i18n="recommendations.otherGames">Other Games</h3>
            <p class="recommendations-subtitle" data-i18n="recommendations.discoverMore">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/klondike-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/freecell-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">FreeCell Solitaire</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/chess" class="recommendation-card" data-category="strategy">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/sudoku-online" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
                <a href="/bubble-float" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">💭</div>
                        <h4 class="game-name">Bubble Float</h4>
                        <div class="game-rating">⭐ 4.1</div>
                    </div>
                </a>
                <a href="/particle-trail" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">✨</div>
                        <h4 class="game-name">Particle Trail</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/blackjack-simulator/js/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>