<!DOCTYPE html>
<html lang="en">

<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WZL8VKTQ0M"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WZL8VKTQ0M');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Chess Master">
    <meta name="application-name" content="Chess Master">
    <meta name="theme-color" content="#1a4b3a">
    <meta name="msapplication-TileColor" content="#1a4b3a">
    <meta name="msapplication-navbutton-color" content="#1a4b3a">
    <meta name="screen-orientation" content="landscape">
    <meta name="orientation" content="landscape">
    <title>Chess Master - Professional Chess Game</title>
    <meta name="description" content="Experience the most professional Chess game with beautiful 3D boards, smooth piece animations, and intelligent AI opponents.">
    <meta name="keywords" content="chess, chess game, online chess, chess master, strategy game, board game">
    <link rel="stylesheet" href="/chess/css/styles.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
</head>

<body>
    <!-- SEO H1 for search engines (hidden from users) -->
    <h1 style="position: absolute; left: -9999px; top: -9999px; visibility: hidden;">Chess Master - Professional Strategy Board Game</h1>
    
    <div class="rotate-prompt" id="rotate-prompt">
        <div class="rotate-content">
            <div class="rotate-icon">📱</div>
            <div class="rotate-text">Please rotate your device to landscape mode for the best chess experience</div>
            <div class="rotate-arrow">↻</div>
        </div>
    </div>

    <div class="chess-container">
        <div class="top-status">
            <div class="left-controls">
                <a class="home-button" href="/">🏠 Home</a>
            </div>
            <div class="right-controls">
                <div class="fullscreen-button" id="fullscreen-button" title="Toggle Fullscreen">
                    <span class="fullscreen-icon" id="fullscreen-icon">⛶</span>
                </div>
            </div>
        </div>

        <div class="chess-table">
            <div class="game-area" id="game-area">
                <!-- Game Settings Panel (shown initially) -->
                <div class="game-settings" id="game-settings">
                    <h2 class="settings-title">Chess Master</h2>
                    
                    <div class="setting-group">
                        <label class="setting-label">Game Mode</label>
                        <div class="setting-options">
                            <button class="setting-btn active" data-mode="vs-ai">
                                <span>🤖 vs AI</span>
                            </button>
                            <button class="setting-btn" data-mode="vs-player">
                                <span>👥 vs Player</span>
                            </button>
                        </div>
                    </div>

                    <div class="setting-group" id="difficulty-setting">
                        <label class="setting-label">AI Difficulty</label>
                        <div class="setting-options">
                            <button class="setting-btn" data-difficulty="easy">
                                <span>Easy</span>
                            </button>
                            <button class="setting-btn active" data-difficulty="medium">
                                <span>Medium</span>
                            </button>
                            <button class="setting-btn" data-difficulty="hard">
                                <span>Hard</span>
                            </button>
                        </div>
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">Play as</label>
                        <div class="setting-options">
                            <button class="setting-btn active" data-color="white">
                                <span>⚪ White</span>
                            </button>
                            <button class="setting-btn" data-color="black">
                                <span>⚫ Black</span>
                            </button>
                            <button class="setting-btn" data-color="random">
                                <span>🎲 Random</span>
                            </button>
                        </div>
                    </div>

                    <button class="start-game-btn" id="start-game-btn">
                        <div class="btn-text">START GAME</div>
                    </button>
                </div>

                <!-- Game Status -->
                <div class="game-status" id="game-status" style="display: none;">
                    White to move
                </div>

                <!-- Black Player Info -->
                <div class="player-info black-player" id="black-player" style="display: none;">
                    <div class="player-details" id="black-details">
                        <div class="player-avatar">♛</div>
                        <div class="player-name" id="black-name">Black Player</div>
                    </div>
                    <div class="captured-pieces" id="black-captured"></div>
                </div>

                <!-- Chessboard -->
                <div class="chessboard-container" style="display: none;" id="chessboard-container">
                    <div class="chessboard" id="chessboard">
                        <!-- 64 squares will be generated by JavaScript -->
                    </div>
                </div>

                <!-- White Player Info -->
                <div class="player-info white-player" id="white-player" style="display: none;">
                    <div class="player-details active" id="white-details">
                        <div class="player-avatar">♔</div>
                        <div class="player-name" id="white-name">White Player</div>
                    </div>
                    <div class="captured-pieces" id="white-captured"></div>
                </div>
            </div>
        </div>

        <!-- Game Controls (floating) -->
        <div class="game-controls" id="game-controls" style="display: none;">
            <button class="control-btn new-game-btn" id="new-game-btn">
                <span>🎮</span>
                <span>New Game</span>
            </button>
            <button class="control-btn undo-btn" id="undo-btn">
                <span>↶</span>
                <span>Undo</span>
            </button>
            <button class="control-btn settings-btn" id="settings-btn">
                <span>⚙️</span>
                <span>Settings</span>
            </button>
        </div>
    </div>

    <!-- Game Over Modal -->
    <div class="modal-overlay" id="game-over-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="game-result-title">Game Over</h2>
            </div>
            <div class="modal-body">
                <p id="game-result-message">The game has ended!</p>
            </div>
            <div class="modal-footer">
                <button class="modal-btn primary" id="play-again-btn">
                    <span>🎮</span>
                    <span>Play Again</span>
                </button>
                <button class="modal-btn secondary" id="main-menu-btn">
                    <span>🏠</span>
                    <span>Main Menu</span>
                </button>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Chess Master - Professional Chess Game Online</h2>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">♟️ Premium Chess Experience</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Experience the ultimate chess game with our professionally designed interface. Featuring a beautiful 3D chessboard, smooth piece animations, and intelligent AI opponents that adapt to your skill level.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Our chess game supports both single-player against AI and multiplayer modes, offering a complete chess experience for players of all levels from beginners to grandmasters.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 Advanced Game Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Smart AI Opponents:</strong> Multiple difficulty levels</li>
                    <li><strong style="color: #ffffff;">Move Validation:</strong> Real-time legal move checking</li>
                    <li><strong style="color: #ffffff;">Piece Animations:</strong> Smooth movement effects</li>
                    <li><strong style="color: #ffffff;">Game History:</strong> Move tracking and undo system</li>
                    <li><strong style="color: #ffffff;">Responsive Design:</strong> Perfect on all devices</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">♛ Strategic Gameplay & Learning</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Master the art of chess with our interactive game that teaches strategy while you play. The AI analyzes your moves and adapts its difficulty to provide the perfect challenge for improving your skills.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Features include move suggestions for beginners, threat detection highlighting, and detailed game analysis to help you understand chess strategy and improve your game.</p>
        </div>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 Tournament Ready</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Standard tournament rules</li>
                    <li>Professional time controls</li>
                    <li>FIDE-compliant gameplay</li>
                    <li>Advanced position analysis</li>
                </ul>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">📚 Learning Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Interactive move hints</li>
                    <li>Opening book database</li>
                    <li>Endgame practice mode</li>
                    <li>Position evaluation</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌟 The Ultimate Chess Experience</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Our professional chess game combines cutting-edge technology with traditional gameplay. Enjoy stunning visuals, intelligent AI opponents, and a sophisticated interface that makes chess accessible to everyone.</p>
            <p style="color: #f0f0f0;">Whether you're learning the basics or preparing for tournament play, our chess game provides the perfect platform to develop your skills and enjoy the world's greatest strategy game!</p>
        </div>
    </div>

                                                                                                        <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Board & Tile Games</h3>
            <p class="recommendations-subtitle">Continue exploring board & tile games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/mahjong-solitaire" class="recommendation-card" data-category="board-games">
                    <div class="card-image">
                        <div class="game-icon">🀄</div>
                        <h4 class="game-name">Mahjong Solitaire</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-online" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/free-bet-blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-card-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/klondike-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/freecell-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">FreeCell Solitaire</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/sudoku-online" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/chess/js/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>