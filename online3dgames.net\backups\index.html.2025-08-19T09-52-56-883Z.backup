<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-WZL8VKTQ0M"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-WZL8VKTQ0M');
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solitaire Games Collection - Master Classic Card Patience Games Online</title>
    <meta name="description"
        content="Master Solitaire with our complete collection of classic patience card games. Play Klondike Solitaire, Spider Solitaire, and FreeCell Solitaire. Strategic gameplay and traditional rules included.">
    <meta name="keywords"
        content="solitaire, patience, card game, klondike solitaire, spider solitaire, freecell solitaire, single player card games, classic solitaire, online solitaire">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Tags -->
    <meta property="og:title" content="Solitaire Games Collection - Master Classic Card Patience Games Online">
    <meta property="og:description"
        content="Master Solitaire with our complete collection of classic patience card games. Play Klondike, Spider, and FreeCell Solitaire.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://flowfray.com/solitaire">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Solitaire Games Collection">
    <meta name="twitter:description" content="Master Solitaire with our complete collection of classic patience card games.">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://flowfray.com/solitaire">

    <link rel="stylesheet" href="/assets/css/main.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <meta name="theme-color" content="#0a0a0a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Solitaire Games Collection",
        "description": "Complete collection of classic Solitaire patience card games including Klondike Solitaire, Spider Solitaire, and FreeCell Solitaire variants",
        "url": "https://flowfray.com/solitaire",
        "mainEntity": {
            "@type": "ItemList",
            "name": "Solitaire Game Variants",
            "numberOfItems": 3,
            "itemListElement": [
                {
                    "@type": "Game",
                    "position": 1,
                    "name": "Klondike Solitaire",
                    "description": "Classic single-player card game with traditional seven-column tableau layout",
                    "url": "https://flowfray.com/klondike-solitaire"
                },
                {
                    "@type": "Game", 
                    "position": 2,
                    "name": "Spider Solitaire",
                    "description": "Challenging multi-suit solitaire with strategic sequence building",
                    "url": "https://flowfray.com/spider-solitaire"
                },
                {
                    "@type": "Game",
                    "position": 3, 
                    "name": "FreeCell Solitaire",
                    "description": "Strategic solitaire variant with four free cells for tactical card placement",
                    "url": "https://flowfray.com/freecell-solitaire"
                }
            ]
        }
    }
    </script>

    <style>
        .solitaire-hero {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #1a1a1a 100%);
            padding: 120px 20px 60px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .solitaire-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cards" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="none"/><path d="M2 2h16v16H2z" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23cards)"/></svg>') repeat;
            opacity: 0.3;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 900;
            color: #ffd700;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5), 2px 2px 4px rgba(0, 0, 0, 0.8);
            margin-bottom: 20px;
            letter-spacing: -0.02em;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: #e0e0e0;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .games-section {
            max-width: 1200px;
            margin: 60px auto;
            padding: 0 20px;
        }

        .section-title {
            font-size: 2.5rem;
            color: #ffffff;
            text-align: center;
            margin-bottom: 50px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .game-details {
            margin-top: 60px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
        }

        .detail-card {
            background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
        }

        .detail-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 215, 0, 0.1);
            border-color: rgba(255, 215, 0, 0.4);
        }

        .detail-card h3 {
            color: #ffd700;
            font-size: 1.3rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .detail-card p {
            color: #d0d0d0;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .about-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            padding: 60px 20px;
            margin: 60px 0;
        }

        .about-content {
            max-width: 1000px;
            margin: 0 auto;
        }

        .about-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .about-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.1);
        }

        .about-card h3 {
            color: #ffd700;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .about-card p {
            color: #e0e0e0;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .detail-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .about-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>

<body>
    <h1 style="position: absolute; left: -9999px; top: -9999px; visibility: hidden;">Solitaire</h1>

    <!-- Navigation -->
    <nav class="top-nav">
        <div class="nav-container">
            <div class="nav-left">
                <a href="/" class="logo">
                    <span class="logo-icon">🎰</span>
                    <span class="logo-text">Flow Fray</span>
                </a>
            </div>
            <div class="nav-center">
                <div class="search-bar">
                    <input type="text" placeholder="Search games..." class="search-input">
                    <button class="search-btn">🔍</button>
                </div>
            </div>
            <div class="nav-right">
                <a href="/" class="nav-btn">Home</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="solitaire-hero">
        <div class="hero-content">
            <h2 class="hero-title">Solitaire</h2>
            <p class="hero-subtitle">Master the timeless art of patience card games. Experience classic single-player gameplay with multiple solitaire variants, strategic thinking, and traditional rules that have entertained players for centuries.</p>
        </div>
    </section>

    <!-- Games Section -->
    <section class="games-section">
        <h2 class="section-title">Choose Your Solitaire Experience</h2>

        <!-- Game Details Section -->
        <div class="game-details">
            <div class="detail-grid">
                <div class="detail-card">
                    <a href="/klondike-solitaire" style="text-decoration: none; color: inherit;">
                        <h3>🃏 Klondike Solitaire</h3>
                        <p>The classic solitaire experience with seven-column tableau layout, foundation building, and traditional draw-three stock pile gameplay with strategic card placement.</p>
                    </a>
                </div>
                <div class="detail-card">
                    <a href="/spider-solitaire" style="text-decoration: none; color: inherit;">
                        <h3>🕷️ Spider Solitaire</h3>
                        <p>Challenge yourself with this complex variant featuring ten tableau columns, sequence building from King to Ace, and multiple difficulty levels with suit variations.</p>
                    </a>
                </div>
                <div class="detail-card">
                    <a href="/freecell-solitaire" style="text-decoration: none; color: inherit;">
                        <h3>🃏 FreeCell Solitaire</h3>
                        <p>Strategic gameplay with four free cells for tactical card storage, eight tableau columns, and foundation building requiring careful planning and foresight.</p>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- About Solitaire Section -->
    <section class="about-section">
        <div class="about-content">
            <h2 class="section-title">Master the Art of Solitaire</h2>
            <div class="about-grid">
                <div class="about-card">
                    <h3>Game History</h3>
                    <p>Solitaire, also known as Patience, originated in Northern Europe during the late 18th century. The games gained popularity in France and Germany before spreading worldwide. Klondike Solitaire became the most famous variant after being included with Microsoft Windows, introducing millions to the timeless appeal of single-player card games.</p>
                </div>

                <div class="about-card">
                    <h3>Strategic Gameplay</h3>
                    <p>Solitaire games combine luck with strategic thinking. Success requires careful planning, pattern recognition, and understanding of probability. Each variant offers unique challenges: Klondike emphasizes foundation building, Spider focuses on sequence creation, and FreeCell rewards tactical use of free cells.</p>
                </div>

                <div class="about-card">
                    <h3>Klondike Rules</h3>
                    <p>The classic solitaire features seven tableau columns with cards dealt face-down except the top cards. Build foundations from Ace to King by suit, and arrange tableau sequences in descending order with alternating colors. Draw from the stock pile to find needed cards.</p>
                </div>

                <div class="about-card">
                    <h3>Spider Complexity</h3>
                    <p>Spider Solitaire uses two decks with ten tableau columns. Build sequences from King to Ace within the tableau, regardless of suit initially. Complete sequences of the same suit are automatically removed. The challenge increases with multiple suits requiring advanced planning.</p>
                </div>

                <div class="about-card">
                    <h3>FreeCell Strategy</h3>
                    <p>FreeCell offers the highest skill component among solitaire variants. With four free cells for temporary storage and all cards visible from the start, nearly every deal is winnable with perfect play. Success depends on efficient use of free cells and careful sequence planning.</p>
                </div>

                <div class="about-card">
                    <h3>Mental Benefits</h3>
                    <p>Solitaire games provide excellent mental exercise, improving concentration, pattern recognition, and strategic thinking. The meditative nature of card arrangement offers stress relief while the challenge of solving each deal provides satisfying mental stimulation and achievement.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Solitaire Games</h3>
            <p class="recommendations-subtitle">Explore different Solitaire variants</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/klondike-solitaire" class="recommendation-card" data-category="solitaire">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Klondike Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="solitaire">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/freecell-solitaire" class="recommendation-card" data-category="solitaire">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">FreeCell Solitaire</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/chess" class="recommendation-card" data-category="board-games">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/sudoku-online" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
                <a href="/bubble-float" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">💭</div>
                        <h4 class="game-name">Bubble Float</h4>
                        <div class="game-rating">⭐ 4.1</div>
                    </div>
                </a>
                <a href="/virtual-pet" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🐾</div>
                        <h4 class="game-name">Virtual Pet</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>
